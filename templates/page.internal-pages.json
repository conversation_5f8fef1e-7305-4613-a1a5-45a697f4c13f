/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-page",
      "blocks": {
        "text_A8F3Wr": {
          "type": "text",
          "name": "t:names.heading",
          "settings": {
            "text": "<p>{{ closest.page.title }}</p>",
            "width": "fit-content",
            "max_width": "normal",
            "alignment": "left",
            "type_preset": "h1",
            "font": "var(--font-body--family)",
            "font_size": "1rem",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "var(--color-foreground)",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0,
            "custom_css_class": ""
          },
          "blocks": {}
        },
        "text_KMwtTr": {
          "type": "text",
          "name": "t:names.text",
          "settings": {
            "text": "{{ closest.page.content }}",
            "width": "fit-content",
            "max_width": "normal",
            "alignment": "left",
            "type_preset": "rte",
            "font": "var(--font-body--family)",
            "font_size": "1rem",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "var(--color-foreground)",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0,
            "custom_css_class": ""
          },
          "blocks": {}
        }
      },
      "block_order": [
        "text_A8F3Wr",
        "text_KMwtTr"
      ],
      "disabled": true,
      "settings": {
        "content_direction": "column",
        "gap": 32,
        "color_scheme": "",
        "padding-block-start": 40,
        "padding-block-end": 80
      }
    },
    "internal_page_layout_section_FTrXwJ": {
      "type": "internal-page-layout-section",
      "blocks": {
        "link_wMYGwX": {
          "type": "link",
          "settings": {
            "link_label": "תקנון האתר ותנאי השימוש",
            "link": "#1"
          }
        },
        "link_3aiMPt": {
          "type": "link",
          "settings": {
            "link_label": "מדיניות הפרטיות",
            "link": "#2"
          }
        },
        "link_bYdnaF": {
          "type": "link",
          "settings": {
            "link_label": "הצהרת נגישות",
            "link": "#2"
          }
        }
      },
      "block_order": [
        "link_wMYGwX",
        "link_3aiMPt",
        "link_bYdnaF"
      ],
      "name": "IntPage Layout privacy-policy",
      "settings": {
        "override_title": "{{ page.title }}",
        "updated_text": "עדכון אחרון: 12/01/2025",
        "sidebar_title": ""
      }
    },
    "internal_page_layout_section_TntmNd": {
      "type": "internal-page-layout-section",
      "blocks": {
        "link_jkaARf": {
          "type": "link",
          "settings": {
            "link_label": "תקנון האתר ותנאי השימוש",
            "link": "#1"
          }
        },
        "link_ndHfej": {
          "type": "link",
          "settings": {
            "link_label": "מדיניות הפרטיות",
            "link": "#2"
          }
        },
        "link_Ly9UGw": {
          "type": "link",
          "settings": {
            "link_label": "הצהרת נגישות",
            "link": "#2"
          }
        }
      },
      "block_order": [
        "link_jkaARf",
        "link_ndHfej",
        "link_Ly9UGw"
      ],
      "name": "IntPage Layout Terms Links",
      "settings": {
        "override_title": "{{ page.title }}",
        "updated_text": "עדכון אחרון: 12/01/2025",
        "sidebar_title": ""
      }
    }
  },
  "order": [
    "main",
    "internal_page_layout_section_FTrXwJ",
    "internal_page_layout_section_TntmNd"
  ]
}
